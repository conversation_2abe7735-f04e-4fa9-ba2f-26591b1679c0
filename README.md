# 🕹️ PLATFORMER ADVENTURE

**Version 1.0**

A creative 5-level platformer built entirely using Amazon Q CLI—no initial codebase, just prompting and iteration.

---

## 🎮 Game Overview

Platformer Adventure is a browser-based side-scrolling game where players navigate through five progressively harder levels filled with coins, spikes, flying enemies, and moving platforms. The entire game was created using AI-generated prompts with Amazon Q CLI.

---

## 🌟 Features

* 🎮 **Double Jump Mechanics**
* 👾 **Flying Enemies with Wing Animation**
* 💰 **Coin Collection System with Visual Effects**
* ⚠️ **Spikes and Hazards That Cause Game Over**
* 🏆 **Five Hand-Crafted Levels with Progressive Difficulty**
* 🎯 **Start Screen, Game Over Screen, and Level Transition UI**
* 🧠 **AI-generated code, logic, and styling**

---

## 🧠 Why a Platformer?

A platformer pushes key game development concepts like:

* Gravity & jumping
* Platform collision detection
* Enemy movement and collision
* Coin and score management
* Level design and pacing

Amazon Q CLI let me focus purely on creativity. No setup, no boilerplate—just describe what you want.

---

## 💬 Prompting Techniques

**Effective prompting examples:**

* `Create a red square player that can move and jump`
* `Add a start screen with a play button and a stylized title`
* `Design flying enemies with animated wings and eyes`
* `Make a HUD showing score, lives, coins, and level number`
* `Create 5 progressively harder levels with moving platforms and spikes`

These instructions produced functional code immediately, with zero manual configuration.

---

## 🧩 AI Solves Classic Challenges

### 🗂️ Game State Management

```html
<div id="startScreen">
  <button class="play-button" onclick="startGame()">Play Game</button>
</div>
```

* Built-in support for Start, Game Over, and Level Complete screens
* Buttons automatically control game state transitions

### 🏃 Player Movement & Jump Physics

```js
if (jumpKey && !jumpPressed) {
  if (onGround) {
    playerVelY = -jumpPower;
    canDoubleJump = true;
  } else if (canDoubleJump) {
    playerVelY = -doubleJumpPower;
    canDoubleJump = false;
  }
}
```

* AI wrote functional physics with gravity, double-jump logic, and grounded detection

### 👾 Enemy Animations

```css
@keyframes wingFlap {
  0%   { transform: rotateZ(-20deg); }
  100% { transform: rotateZ(20deg); }
}
```

* Enemies include animated wings and eyes, with complete hit detection logic

---

## ⚙️ Development Automation

AI generated:

* 5 unique levels with increasing difficulty
* Moving platforms and coins placed on top
* Hazards like spikes added programmatically

### 🧱 Example

```js
createMovingPlatform(300, 500, 80, 15, 100, 0, 1.5);
createCoin(340, 470); // Coin on moving platform
```

---

## ✨ Interesting AI-Generated Solutions

### 💰 Coins with Glow & Spin

```css
.coin {
  animation: spin 2s linear infinite, coinGlow 1.5s ease-in-out infinite alternate;
}
```

### 😀 Player Style & Effects

```css
#player::before {
  background: radial-gradient(circle at 25% 40%, #2f3542 3px, transparent 3px);
}
```

* Jumping triggers spin or bounce animations
* Player has cartoon eyes and mouth

---

## 📸 Screenshots

### 🏁 Start Screen

Game title, instructions, and animated “Play Game” button
![Start Screen](images/start-screen.png)

### 🕹️ Gameplay

* 5 Levels
* Enemies with flapping wings
* Moving platforms and coin pickups
  ![Gameplay](images/gameplay1.png)
  ![Gameplay](images/gameplay2.gif)

---

## 🚀 Getting Started

### 📁 File Structure

```
platformer-adventure/
├── index.html        # Main game page (AI-generated)
├── styles.css        # Game styles and animations
├── game.js           # Game logic (player, levels, AI-generated)
├── images/           # Screenshots for README
└── README.md         # This file
```

### 🧰 Requirements

* Modern browser (Chrome, Firefox, Safari)
* No dependencies or installation required

### ▶️ Run the Game

Open `index.html` in a web browser and click **Play Game**.

---

## 🏁 Final Takeaways

Using Amazon Q CLI to build this game allowed me to:

* ✅ Skip boilerplate and setup
* ✅ Focus on game mechanics and creativity
* ✅ Prototype and iterate rapidly
* ✅ Automate level design and interactions
* ✅ Add polished UI, animations, and effects

If you're curious about AI-assisted game development, **Amazon Q CLI is the fastest, most creative way to build your ideas.**

---

## 📄 License

Open source. Free to use, remix, and build on.

---

## 🎯 Credits

**Game Design & Code Prompts**: Created via Amazon Q CLI
**Art Style**: Prompted retro-cartoon blend
**Development**: Browser-native HTML, CSS, and JavaScript
**Powered By**: Amazon Q Developer Tools

---

Let me know if you want me to export this into a `README.md` file!

